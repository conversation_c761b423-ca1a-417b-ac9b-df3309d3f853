<script setup lang="ts">
import { ref, computed } from 'vue'
import { useUserStore } from '@/stores'

const userStore = useUserStore()

// 响应式数据
const currentDimension = ref(10) // 当前维度进度
const selectedTab = ref('你的') // 默认选择你的标签
const isExpanded = ref(false) // 控制信息展开/收缩状态
const showCompatibilityModal = ref(false) // 控制匹配度弹窗
const showCoreDimensionModal = ref(false) // 控制核心维度弹窗
const showPersonalityModal = ref(false) // 控制性格类型弹窗

// 6种核心性格类型定义
const personalityTypes = {
  'Explorer': {
    name: '探索者',
    name_en: 'Explorer',
    traits: ["好奇", "冒险", "创新"],
    labs: ['好奇心强', '富有冒险精神', '创新思维'],
    dimensions: ['发现', '漫游', '创造', '改变', '真理'],
    description: '你天生具有强烈的好奇心和探索欲望，总是渴望发现新的可能性。你喜欢在未知的领域中漫游，用创新的思维方式创造独特的解决方案。你的探索精神激励着周围的人勇敢地走出舒适圈。',
    tendency: ['高随性','高分析','中等协作'],
    radarProfile: {
      'Spontaneous': 22,    // 高随性
      'Collaborative': 16,  // 中等协作性
      'Realist': 12,        // 较低现实主义
      'Logical': 18,        // 中等逻辑性
      'Analytical': 22,     // 高分析性
      'Introvert': 6        // 低内向性
    },
    partner_traits: ['好奇的', '勇敢的', '创新的'],
    partner_description: '带你发现未知的可能性和新的视角'
  },
  'Guardian': {
    name: '守护者',
    name_en: 'Guardian',
    traits: ["责任", "稳定", "关怀"],
    labs: ['有责任心', '值得信赖', '关怀他人'],
    dimensions: ['关怀', '秩序', '家园', '和平', '价值'],
    description: '你是天生的守护者，以关怀他人为己任，致力于创造稳定有序的环境。你重视传统和秩序，总是为身边的人营造温暖的家园感。你的责任心和可靠性让人们感到安全和被保护。',
    tendency: ['低随性', '高协作', '高务实'],
    radarProfile: {
      'Spontaneous': 8,     // 低随性
      'Collaborative': 24,  // 高协作性
      'Realist': 22,        // 高现实主义
      'Logical': 18,        // 中等逻辑性
      'Analytical': 12,     // 较低分析性
      'Introvert': 12       // 中等内向性
    },
    partner_traits: ['可靠的', '温暖的', '有条理的'],
    partner_description: '总是在你需要的时候提供支持和指导'
  },
  'Creator': {
    name: '创造者',
    name_en: 'Creator',
    traits: ["艺术", "想象", "表达"],
    labs: ['富有创意', '艺术天赋', '独特视角'],
    dimensions: ['创造', '梦想', '风格', '音乐', '感受'],
    description: '你拥有丰富的想象力和独特的艺术天赋，善于将梦想转化为现实。你追求个人风格的表达，总能从独特的角度看待世界。你的创造力不仅体现在艺术作品中，更渗透到生活的方方面面。',
    tendency: ['高随性', '中等内向', '高分析'],
    radarProfile: {
      'Spontaneous': 20,    // 高随性
      'Collaborative': 14,  // 中等协作性
      'Realist': 10,        // 较低现实主义
      'Logical': 16,        // 中等逻辑性
      'Analytical': 20,     // 高分析性
      'Introvert': 16       // 中等内向性
    },
    partner_traits: ['富有想象力的', '艺术的', '独特的'],
    partner_description: '激发你的创造力，帮你实现艺术梦想'
  },
  'Leader': {
    name: '领导者',
    name_en: 'Leader',
    traits: ["影响", "决策", "目标"],
    labs: ['天生领袖', '目标导向', '影响力强'],
    dimensions: ['领导', '力量', '影响', '声音', '成长'],
    description: '你天生具备领导才能，善于引导他人朝着共同目标前进。你拥有强大的影响力和决策能力，能够在关键时刻做出正确的判断。你的领导风格激励团队发挥最大潜能，创造积极的影响。',
    tendency: ['低内向', '高逻辑', '中等协作'],
    radarProfile: {
      'Spontaneous': 14,    // 中等随性
      'Collaborative': 16,  // 中等协作性
      'Realist': 20,        // 高现实主义
      'Logical': 24,        // 高逻辑性
      'Analytical': 18,     // 中等分析性
      'Introvert': 4        // 低内向性
    },
    partner_traits: ['果断的', '有魅力的', '目标明确的'],
    partner_description: '引导你发挥领导潜能，实现远大目标'
  },
  'Thinker': {
    name: '思考者',
    name_en: 'Thinker',
    traits: ["理性", "深度", "独立"],
    labs: ['深度思考', '理性分析', '追求真理'],
    dimensions: ['真理', '学习', '反思', '观察', '页面'],
    description: '你是一个深度思考者，总是在追求真理和知识的道路上不断前行。你善于理性分析复杂问题，通过反思获得深刻的洞察。你的独立思考能力让你能够看透事物的本质，为他人提供智慧的指导。',
    tendency: ['高内向', '高逻辑', '高分析'],
    radarProfile: {
      'Spontaneous': 10,    // 较低随性
      'Collaborative': 12,  // 较低协作性
      'Realist': 18,        // 中等现实主义
      'Logical': 24,        // 高逻辑性
      'Analytical': 24,     // 高分析性
      'Introvert': 8        // 高内向性
    },
    partner_traits: ['理性的', '深刻的', '睿智的'],
    partner_description: '与你一起探索知识的深度和真理的本质'
  },
  'Connector': {
    name: '连接者',
    name_en: 'Connector',
    traits: ["社交", "和谐", "共情"],
    labs: ['善于社交', '和谐共处', '情感丰富'],
    dimensions: ['连接', '联结', '平衡', '浪漫', '季节'],
    description: '你天生具有强大的社交能力和共情力，善于在人与人之间建立深层的连接。你追求和谐平衡，总能在冲突中找到共同点。你的情感丰富性让你能够理解他人的感受，创造温暖包容的人际关系。',
    tendency: ['低内向', '高协作', '中等务实'],
    radarProfile: {
      'Spontaneous': 16,    // 中等随性
      'Collaborative': 24,  // 高协作性
      'Realist': 16,        // 中等现实主义
      'Logical': 14,        // 中等逻辑性
      'Analytical': 12,     // 较低分析性
      'Introvert': 14       // 低内向性
    },
    partner_traits: ['友善的', '理解的', '包容的'],
    partner_description: '帮你建立深层连接，创造和谐关系'
  }
}

// 用户当前属性值（这里可以从用户数据或AI交流结果中获取）
const userRadarData = ref([
  { label: 'Spontaneous', label_zh: '随性', value: 18 },
  { label: 'Collaborative', label_zh: '协作', value: 16 },
  { label: 'Realist', label_zh: '务实', value: 14 },
  { label: 'Logical', label_zh: '逻辑', value: 20 },
  { label: 'Analytical', label_zh: '分析', value: 18 },
  { label: 'Introvert', label_zh: '内向', value: 10 }
])

// 动态计算用户性格类型
const getUserPersonalityType = () => {
  let bestMatch = 'Explorer'
  let minDistance = Infinity
  
  Object.keys(personalityTypes).forEach(type => {
    let distance = 0
    userRadarData.value.forEach(userAttr => {
      const aiValue = personalityTypes[type].radarProfile[userAttr.label]
      distance += Math.pow(userAttr.value - aiValue, 2)
    })
    
    if (distance < minDistance) {
      minDistance = distance
      bestMatch = type
    }
  })
  
  return bestMatch
}

// 获取当前用户性格类型
const currentPersonalityType = computed(() => getUserPersonalityType())

// 获取用户特质
const traits = computed(() => personalityTypes[currentPersonalityType.value].traits)

// 生成雷达图对比数据
const companionRadarData = computed(() => {
  const personalityProfile = personalityTypes[currentPersonalityType.value].radarProfile
  return userRadarData.value.map(userAttr => ({
    label: userAttr.label,
    label_zh: userAttr.label_zh,
    skychart: personalityProfile[userAttr.label],
    you: userAttr.value
  }))
})

// 维度选项
const dimensions = [
  // 第一组
  [
    { name: 'Taste', name_zh: '品味', icon: '🍷' },
    { name: 'Truth', name_zh: '真理', icon: '💎' },
    { name: 'Power', name_zh: '力量', icon: '⚡' }
  ],
  // 第二组  
  [
    { name: 'Pages', name_zh: '页面', icon: '📖' },
    { name: 'Worth', name_zh: '价值', icon: '💰' },
    { name: 'Feel', name_zh: '感受', icon: '💝' }
  ],
  // 第三组
  [
    { name: 'Impact', name_zh: '影响', icon: '🎯' },
    { name: 'Play', name_zh: '游戏', icon: '🎮' },
    { name: 'Create', name_zh: '创造', icon: '🎨' }
  ],
  // 第四组
  [
    { name: 'Season', name_zh: '季节', icon: '🌸' },
    { name: 'Care', name_zh: '关怀', icon: '💖' },
    { name: 'Style', name_zh: '风格', icon: '👗' }
  ],
  // 第五组
  [
    { name: 'Discover', name_zh: '发现', icon: '🔍' },
    { name: 'Balance', name_zh: '平衡', icon: '⚖️' },
    { name: 'Peace', name_zh: '和平', icon: '☮️' }
  ],
  // 第六组
  [
    { name: 'Voice', name_zh: '声音', icon: '🎤' },
    { name: 'Change', name_zh: '改变', icon: '🔄' },
    { name: 'Bond', name_zh: '联结', icon: '🤝' }
  ],
  // 第七组
  [
    { name: 'Dream', name_zh: '梦想', icon: '🌙' },
    { name: 'Order', name_zh: '秩序', icon: '📋' },
    { name: 'Home', name_zh: '家园', icon: '🏠' }
  ],
  // 第八组
  [
    { name: 'Learn', name_zh: '学习', icon: '📚' },
    { name: 'Romance', name_zh: '浪漫', icon: '💕' },
    { name: 'Wander', name_zh: '漫游', icon: '🌍' }
  ],
  // 第九组
  [
    { name: 'Watch', name_zh: '观察', icon: '👁️' },
    { name: 'Connect', name_zh: '连接', icon: '🔗' },
    { name: 'Reflect', name_zh: '反思', icon: '🪞' }
  ],
  // 第十组
  [
    { name: 'Lead', name_zh: '领导', icon: '👑' },
    { name: 'Music', name_zh: '音乐', icon: '🎵' },
    { name: 'Grow', name_zh: '成长', icon: '🌱' }
  ]
]

// 生成雷达图网格路径
const getGridPaths = () => {
  const centerX = 150
  const centerY = 150
  const maxRadius = 100
  const paths = []
  
  // 生成同心六边形网格线
  for (let level = 1; level <= 5; level++) {
    const radius = (maxRadius * level) / 5
    let path = `M ${centerX + radius} ${centerY}`
    
    for (let i = 1; i <= 6; i++) {
      const angle = (i * 60) * Math.PI / 180
      const x = centerX + radius * Math.cos(angle)
      const y = centerY + radius * Math.sin(angle)
      path += ` L ${x} ${y}`
    }
    path += ' Z'
    paths.push(path)
  }
  
  // 生成从中心到各顶点的射线
  for (let i = 0; i < 6; i++) {
    const angle = (i * 60) * Math.PI / 180
    const x = centerX + maxRadius * Math.cos(angle)
    const y = centerY + maxRadius * Math.sin(angle)
    paths.push(`M ${centerX} ${centerY} L ${x} ${y}`)
  }
  
  return paths
}

// 生成雷达图数据路径
const getRadarPath = () => {
  const centerX = 150
  const centerY = 150
  const maxRadius = 100
  
  let path = ''
  
  radarData.forEach((item, index) => {
    const angle = (index * 60) * Math.PI / 180
    const radius = (item.value / 100) * maxRadius
    const x = centerX + radius * Math.cos(angle)
    const y = centerY + radius * Math.sin(angle)
    
    if (index === 0) {
      path = `M ${x} ${y}`
    } else {
      path += ` L ${x} ${y}`
    }
  })
  
  path += ' Z'
  return path
}

// 生成雷达图网格路径（6边形）
const getCompanionGridPaths = () => {
  const centerX = 150
  const centerY = 150
  const maxRadius = 100
  const paths = []
  
  // 生成同心六边形网格线 - 只要3圈
  for (let level = 1; level <= 3; level++) {
    const radius = (maxRadius * level) / 3
    let path = ''
    
    for (let i = 0; i < 6; i++) {
      const angle = ((i * 60) - 90) * Math.PI / 180  // 从顶部开始
      const x = centerX + radius * Math.cos(angle)
      const y = centerY + radius * Math.sin(angle)
      
      if (i === 0) {
        path = `M ${x} ${y}`
      } else {
        path += ` L ${x} ${y}`
      }
    }
    path += ' Z'
    paths.push(path)
  }
  
  // 移除从中心到各顶点的射线 - 不要角线
  
  return paths
}

// 生成Skychart雷达图数据路径
const getSkychartRadarPath = () => {
  const centerX = 150
  const centerY = 150
  const maxRadius = 100
  
  let path = ''
  
  companionRadarData.value.forEach((item, index) => {
    const angle = ((index * 60) - 90) * Math.PI / 180
    const radius = (item.skychart / 32) * maxRadius
    const x = centerX + radius * Math.cos(angle)
    const y = centerY + radius * Math.sin(angle)
    
    if (index === 0) {
      path = `M ${x} ${y}`
    } else {
      path += ` L ${x} ${y}`
    }
  })
  
  path += ' Z'
  return path
}

// 生成You雷达图数据路径
const getYouRadarPath = () => {
  const centerX = 150
  const centerY = 150
  const maxRadius = 100
  
  let path = ''
  
  companionRadarData.value.forEach((item, index) => {
    const angle = ((index * 60) - 90) * Math.PI / 180
    const radius = (item.you / 32) * maxRadius
    const x = centerX + radius * Math.cos(angle)
    const y = centerY + radius * Math.sin(angle)
    
    if (index === 0) {
      path = `M ${x} ${y}`
    } else {
      path += ` L ${x} ${y}`
    }
  })
  
  path += ' Z'
  return path
}

// 切换标签页
const switchTab = (tab: string) => {
  selectedTab.value = tab
}

// 开始下一个维度
const startNextDimension = () => {
  console.log('Starting next dimension...')
  // 这里可以添加导航逻辑
}

const goBack = () => {
  if (getCurrentPages().length == 1) {
    uni.reLaunch({ url: '/pages/ai/ai' })
  } else {
    uni.navigateBack({ delta: 1})
  }
}

// 计算真实匹配度
const calculateCompatibility = () => {
  let totalDifference = 0
  let maxPossibleDifference = 0
  
  companionRadarData.value.forEach(item => {
    const difference = Math.abs(item.skychart - item.you)
    totalDifference += difference
    maxPossibleDifference += 32 // 最大差值为32
  })
  
  // 计算相似度百分比 (100% - 平均差异百分比)
  const averageDifference = totalDifference / companionRadarData.value.length
  const compatibility = Math.round(100 - (averageDifference / 32) * 100)
  
  return Math.max(compatibility, 0) // 确保不小于0
}

// 获取核心维度（星灵数值最高的维度）
const getCoredimension = () => {
  let maxValue = 0
  let coreDimension = ''
  
  companionRadarData.value.forEach(item => {
    if (item.skychart > maxValue) {
      maxValue = item.skychart
      coreDimension = item.label_zh
    }
  })
  
  return coreDimension
}

// 计算得到的匹配度和核心维度 - 使用computed
const compatibilityScore = computed(() => calculateCompatibility())
const coreDimensionName = computed(() => getCoredimension())

// 切换展开/收缩状态
const toggleExpand = () => {
  isExpanded.value = !isExpanded.value
}

// 显示匹配度弹窗
const showCompatibilityInfo = () => {
  showCompatibilityModal.value = true
}

// 显示核心维度弹窗
const showCoreDimensionInfo = () => {
  showCoreDimensionModal.value = true
}

// 显示性格类型弹窗
const showPersonalityInfo = () => {
  showPersonalityModal.value = true
}

// 关闭弹窗
const closeModal = () => {
  showCompatibilityModal.value = false
  showCoreDimensionModal.value = false
  showPersonalityModal.value = false
}
</script>

<template>
  <view class="page-wrapper">
    <view class="personality-container">
    <!-- 顶部导航 -->
    <view class="header-nav">
      <view class="back-btn" @click="goBack">
        <image
            style="width: 20px; height: 20px;"
            src="/static/ai/back.png"
            mode="scaleToFill"
        />
      </view>
      <view class="nav-tabs">
        <view 
          v-for="tab in ['你的', '星灵', '时空']" 
          :key="tab"
          :class="['nav-tab', { active: selectedTab === tab }]"
          @click="switchTab(tab)"
        >
          <text class="nav-text">{{ tab }}</text>
        </view>
      </view>
    </view>

    <!-- 你的页面内容 -->
    <view v-if="selectedTab === '你的'" class="main-content">
      <!-- 角色卡片 -->
      <view class="character-card">
        <image class="bg-image" src="https://img1.baidu.com/it/u=3548239810,287346742&fm=253&fmt=auto&app=138&f=JPEG?w=500&h=667" mode="aspectFill" />
        <view class="character-overlay">
          <!-- 角色名称 -->
          <text class="character-name">{{ userStore.profile?.nickname }}</text>
          
          <!-- 特质标签 -->
          <view class="traits-container">
            <view v-for="trait in traits" :key="trait" class="trait-tag">
              <text class="trait-text">{{ trait }}</text>
            </view>
          </view>
          
          <!-- 标题 -->
          <text class="crystal-title">特性</text>
          
          <!-- 下拉箭头 -->
          <view :class="['arrow-down', { expanded: isExpanded }]" @click="toggleExpand">
            <image
                style="width: 100%; height: 100%;"
                src="/static/ai/tasks.png"
                mode="scaleToFill"
            />
          </view>
        </view>

        <!-- 展开信息 -->
        <view :class="['crystal-desc', { expanded: isExpanded }]">
            {{personalityTypes[currentPersonalityType].description }}
        </view>
      </view>

      <!-- 雷达图部分 -->
      <view class="radar-section">
          <text class="radar-title">天空视图
            <image
                style="width: 30rpx; height: 30rpx;margin-left: 10rpx;"
                src="/static/ai/question.png"
                mode="aspectFit"
                @click="showPersonalityInfo"
            />
          </text>
          
          <view class="radar-container">
            <svg class="radar-chart" viewBox="0 0 300 300">
              <!-- 网格线 -->
              <g class="radar-grid">
                <path 
                  v-for="(path, index) in getCompanionGridPaths()" 
                  :key="index"
                  :d="path" 
                  fill="none" 
                  stroke="rgba(255,255,255,0.2)" 
                  stroke-width="1"
                />
              </g>
              
              <!-- You雷达图数据区域 -->
              <path 
                :d="getYouRadarPath()" 
                fill="rgba(33, 150, 243, 0.3)" 
                stroke="#2196f3" 
                stroke-width="2"
              />
              

            </svg>
            
            <!-- 标签 -->
            <view class="radar-labels">
              <view 
                v-for="(item, index) in companionRadarData" 
                :key="index"
                class="radar-label"
                :style="{
                  left: (150 + 120 * Math.cos(((index * 60) - 90) * Math.PI / 180)) + 'px',
                  top: (150 + 120 * Math.sin(((index * 60) - 90) * Math.PI / 180)) + 'px'
                }"
              >
                <text class="label-text">{{ item.label_zh }}</text>
              </view>
            </view>
          </view>
        </view>

      <!-- 维度部分 -->
      <view class="dimensions-section">
        <view class="dimensions-header">
          <text class="dimensions-title">维度</text>
          <view class="progress-container">
            <view class="progress-bar">
              <view class="progress-fill" :style="{ width: currentDimension + '%' }"></view>
            </view>
            <text class="progress-text">{{ currentDimension }}%</text>
          </view>
        </view>
        
        <text class="dimensions-desc">
            深入一个维度，反思它在你生活中的意义，然后获得个性化解读。
        </text>
        
        <!-- 开始按钮 -->
        <view class="start-btn" @click="startNextDimension">
          <text class="start-text">选择维度交流</text>
        </view>
      </view>

      <!-- 维度选项 -->
      <view class="dimensions-grid">
        <view 
          v-for="(row, rowIndex) in dimensions" 
          :key="rowIndex"
          class="dimension-row"
        >
          <view 
            v-for="item in row" 
            :key="item.name"
            class="dimension-item"
          >
            <view class="dimension-icon">
              <text class="icon-emoji">{{ item.icon }}</text>
            </view>
            <text class="dimension-name">{{ item.name_zh }}</text>
          </view>
        </view>
      </view>
    </view>

    <!-- 伙伴页面内容 -->
    <view v-if="selectedTab === '星灵'" class="companion-content">
      <view class="companion-main">
        <!-- 页面标题 -->
        <text class="page-title">{{ userStore.profile.partner.nickName }}</text>
        
        <!-- 特质标签 -->
        <view class="companion-traits">
          <view v-for="trait in userStore.profile.partner.traits" :key="trait" class="companion-trait-tag">
            <text class="companion-trait-text">{{ trait }}</text>
          </view>
        </view>
        
        <!-- Central AI Character - 微动 -->
        <view>
          <view class="rabbit-container">
            <image :src="userStore.profile.partner.character" class="animated-rabbit" style="width: 300rpx; height: 400rpx;" mode="aspectFit" />
          </view>
        </view>
        
                 <!-- 描述文字 -->
         <view class="companion-description">
           <text style="width: 50%;margin: auto;">......{{ userStore.profile.partner.description }}</text>
         </view>
        
      </view>

      <!-- WHY THE ORACLE MATCHED YOU -->
      <view class="oracle-matched-section">

        <!-- 底部信息 -->
        <view class="companion-footer">
          <view class="compatibility-section">
            <view class="compatibility-label">匹配度
              <image
                style="width: 30rpx; height: 30rpx;margin-left: 10rpx;"
                src="/static/ai/question.png"
                mode="scaleToFill"
                @click="showCompatibilityInfo"
            />
            </view>
            <view class="compatibility-value">{{ compatibilityScore }}%</view>
            <view class="compatibility-bar">
              <view class="compatibility-fill" :style="{ width: compatibilityScore + '%' }"></view>
            </view>
          </view>
          
          <view class="core-dimension">
            <view class="core-label">核心维度
              <image
                style="width: 30rpx; height: 30rpx;margin-left: 10rpx;"
                src="/static/ai/question.png"
                mode="scaleToFill"
                @click="showCoreDimensionInfo"
            />
            </view>
            <view class="core-value">{{ coreDimensionName }}</view>
          </view>
        </view>
        
        <text class="oracle-matched-title">“星灵”匹配度</text>
        <view class="radar-comparison-container">
          <svg class="radar-comparison-chart" viewBox="0 0 300 300">
            <!-- 定义遮罩 -->
            <defs>
              <mask id="aiRadarMask">
                <!-- 白色区域显示，黑色区域隐藏 -->
                <rect width="300" height="300" fill="white"/>
                <!-- 用黑色遮罩掉用户雷达区域 -->
                <path 
                  :d="getYouRadarPath()" 
                  fill="black"
                />
              </mask>
            </defs>
            
            <!-- 网格线 -->
            <g class="radar-grid">
              <path 
                v-for="(path, index) in getCompanionGridPaths()" 
                :key="index"
                :d="path" 
                fill="none" 
                stroke="rgba(255,255,255,0.2)" 
                stroke-width="1"
              />
            </g>
            
            <!-- Skychart雷达图填充 (使用遮罩，不显示与用户重叠的部分) -->
            <path 
              :d="getSkychartRadarPath()" 
              fill="rgba(255, 215, 0, 0.3)" 
              stroke="none"
              mask="url(#aiRadarMask)"
            />
            
            <!-- Skychart雷达图边框 (不使用遮罩，始终显示金色边框) -->
            <path 
              :d="getSkychartRadarPath()" 
              fill="none" 
              stroke="#FFD700" 
              stroke-width="2"
            />

             <!-- You雷达图 (在最上层，完全显示) -->
             <path 
               :d="getYouRadarPath()" 
               fill="rgba(255, 255, 255, 0.3)" 
               stroke="#2196f3" 
               stroke-width="2"
             />
                     </svg>
           
           <!-- 雷达图标签 -->
           <view class="radar-comparison-labels">
             <view 
               v-for="(item, index) in companionRadarData" 
               :key="index"
               class="radar-comparison-label"
               :style="{
                 left: (150 + 120 * Math.cos(((index * 60) - 90) * Math.PI / 180)) + 'px',
                 top: (150 + 120 * Math.sin(((index * 60) - 90) * Math.PI / 180)) + 'px'
               }"
             >
               <text class="label-text">{{ item.label_zh }}</text>
             </view>
           </view>
           
           <!-- 图例 -->
           <view class="radar-comparison-legend">
             <view class="legend-item">
               <view class="legend-color" style="background-color: #FFD700;"></view>
               <text class="legend-label">星灵</text>
             </view>
             <view class="legend-item">
               <view class="legend-color" style="background-color: #2196f3;"></view>
               <text class="legend-label">你的</text>
             </view>
           </view>
        </view>
      </view>
    </view>

    <!-- 时空页面内容 -->
    <view v-if="selectedTab === '时空'" class="city-content">
      <view class="city-main">
        <!-- 页面标题 -->
        <view class="city-header">
          <text class="city-subtitle">你的城市</text>
          <text class="city-title">清远大学城</text>
          <text class="city-type">(智慧之城)</text>
        </view>
        
        <!-- 城市图像 -->
        <view class="city-image-container">
          <!-- 天空背景 -->
          <view class="sky-background">
            <view v-for="i in 15" :key="i" class="cloud" :style="{
              left: Math.random() * 100 + '%',
              top: Math.random() * 40 + '%',
              animationDelay: Math.random() * 5 + 's'
            }"></view>
            <!-- 飞行器 -->
            <view class="flying-vehicle"></view>
          </view>
          
          <!-- 主城市 -->
          <view class="city-skyline">
            <view class="city-buildings">
              <!-- 建筑群 -->
              <view class="building building-1"></view>
              <view class="building building-2"></view>
              <view class="building building-3"></view>
              <view class="building building-4"></view>
              <view class="building building-5"></view>
              <!-- 城市光效 -->
              <view class="city-glow"></view>
            </view>
          </view>
        </view>
        
        <!-- 描述文字 -->
        <view class="city-description">
          <text class="description-text">
            你和星灵共同建设的专属城市，这里汇聚着知识与智慧，随着你们关系的发展而不断成长。城市的建筑风格与学术氛围体现着你们对学习和成长的共同追求。
          </text>
        </view>
        
        <!-- 校园核心区域和学术统计 -->
        <view class="campus-core-section">
          <!-- 校园核心建筑 -->
          <view class="campus-landmark-card">
            <!-- 图书馆主建筑 -->
            <view class="library-building">
              <!-- 图书馆外观 -->
              <view class="library-facade">
                <!-- 主体建筑 -->
                <view class="main-structure"></view>
                <!-- 柱子装饰 -->
                <view class="columns">
                  <view v-for="i in 4" :key="i" class="column"></view>
                </view>
                <!-- 台阶 -->
                <view class="steps"></view>
                <!-- 窗户 -->
                <view class="windows">
                  <view v-for="i in 6" :key="i" class="window" :style="{
                    animationDelay: i * 0.3 + 's'
                  }"></view>
                </view>
                <!-- 校园绿化 -->
                <view class="campus-trees">
                  <view v-for="i in 5" :key="i" class="tree" :style="{
                    left: Math.random() * 80 + 10 + '%',
                    animationDelay: Math.random() * 2 + 's'
                  }"></view>
                </view>
                <!-- 学生活动 -->
                <view class="student-activities">
                  <view v-for="i in 3" :key="i" class="student" :style="{
                    left: Math.random() * 60 + 20 + '%',
                    animationDelay: Math.random() * 4 + 's'
                  }"></view>
                </view>
              </view>
            </view>
          </view>
          
          <!-- 学术统计区域 -->
          <view class="academic-stats-section">
            <view class="stats-display">
              <view class="stat-item">
                <text class="stat-value">12k</text>
                <text class="stat-label">星灵</text>
              </view>
              <view class="stats-divider"></view>
            </view>
            
            <!-- 学术活跃度指示器 -->
            <view class="activity-indicator">
              <view class="activity-ring">
                <view class="ring-progress" :style="{ 
                  transform: 'rotate(252deg)' 
                }"></view>
              </view>
              <text class="activity-text">活跃度 85%</text>
            </view>
          </view>
        </view>
        
        <!-- 城市描述和区域缩略图网格 -->
        <view class="city-info-with-thumbnails">
          <view class="city-info-section">
            <text class="city-info-text">
              在清远大学城中，学术建筑与现代化设施和谐共存，营造出浓厚的学习氛围。这里有图书馆、实验楼、学生宿舍和文化中心，见证着你们在知识海洋中的每一次探索与成长。
            </text>
          </view>
          
          <!-- 校园区域缩略图网格 -->
          <view class="thumbnail-grid">
            <view class="thumbnail-row">
              <view class="thumbnail-item">
                <view class="thumbnail-image thumbnail-1" title="图书馆区">
                  <view class="thumb-landscape">
                    <view class="thumb-library-sky"></view>
                    <view class="thumb-library-building"></view>
                    <view class="thumb-library-steps"></view>
                    <view class="thumb-library-trees"></view>
                  </view>
                </view>
              </view>
              <view class="thumbnail-item">
                <view class="thumbnail-image thumbnail-2" title="体育场区">
                  <view class="thumb-landscape">
                    <view class="thumb-sports-sky"></view>
                    <view class="thumb-sports-field"></view>
                    <view class="thumb-sports-track"></view>
                    <view class="thumb-sports-stands"></view>
                  </view>
                </view>
              </view>
            </view>
            <view class="thumbnail-row">
              <view class="thumbnail-item">
                <view class="thumbnail-image thumbnail-3" title="实验楼区">
                  <view class="thumb-landscape">
                    <view class="thumb-lab-sky"></view>
                    <view class="thumb-lab-building"></view>
                    <view class="thumb-lab-windows"></view>
                    <view class="thumb-lab-ground"></view>
                  </view>
                </view>
              </view>
              <view class="thumbnail-item">
                <view class="thumbnail-image thumbnail-4" title="宿舍区">
                  <view class="thumb-landscape">
                    <view class="thumb-dorm-sky"></view>
                    <view class="thumb-dorm-buildings"></view>
                    <view class="thumb-dorm-courtyard"></view>
                    <view class="thumb-dorm-path"></view>
                  </view>
                </view>
              </view>
            </view>
          </view>
        </view>
      </view>
    </view>
  </view>

    <!-- 匹配度弹窗 -->
    <view v-if="showCompatibilityModal" class="modal-overlay" @click="closeModal">
      <view class="modal-content" @click.stop>
        <view class="modal-header">
          <text class="modal-title">匹配度</text>
          <view class="modal-close" @click="closeModal">
            <text class="close-icon">✕</text>
          </view>
        </view>
        <view class="modal-body">
          <text class="modal-text">
            每个星灵也都有自己的核心维度！了解你的核心维度与他们的核心维度如何契合，能够增进你们之间的联系。这种契合会揭示出你的沟通风格、情感需求以及关系模式，帮助你培养共情能力并建立有意义的联系 。
          </text>
        </view>
      </view>
    </view>

    <!-- 核心维度弹窗 -->
    <view v-if="showCoreDimensionModal" class="modal-overlay" @click="closeModal">
      <view class="modal-content" @click.stop>
        <view class="modal-header">
          <text class="modal-title">核心维度</text>
          <view class="modal-close" @click="closeModal">
            <view class="close-icon">
              <image
                src="/static/ai/cancel2.png"
                mode="scaleToFill"
              />
            </view>
          </view>
        </view>
        <view class="modal-body">
          <text class="modal-text">
            星灵的核心维度是其最突出的特质，是让每个星灵独一无二的内在闪光点 。
          </text>
        </view>
      </view>
    </view>

    <!-- 性格类型弹窗 -->
    <view v-if="showPersonalityModal" class="modal-overlay" @click="closeModal">
      <view class="modal-content personality-modal" @click.stop>
        <view class="modal-header">
          <text class="modal-title">类型介绍</text>
          <view class="modal-close" @click="closeModal">
            <view class="close-icon">
              <image
                style="width: 50rpx;height: 50rpx;"
                src="/static/ai/cancel2.png"
                mode="aspectFit"
              />
            </view>
          </view>
        </view>
        <view class="modal-body">

          <!-- 所有性格类型介绍 -->
          <view class="all-personalities">
            
            <view v-for="(type, key) in personalityTypes" :key="key" class="personality-type-item" :class="{ active: key === currentPersonalityType }">
              <view class="type-header">
                <text class="type-name">{{ type.name }} ({{ type.name_en }})</text>
                <view v-if="key === currentPersonalityType" class="current-badge">
                  <text class="badge-text">当前</text>
                </view>
              </view>
              
              <view class="type-traits">
                <view class="traits-row">
                  <text class="traits-label">特质：</text>
                  <view class="traits-tags">
                    <view v-for="trait in type.traits" :key="trait" class="trait-tag">
                      <text class="trait-tag-text">{{ trait }}</text>
                    </view>
                  </view>
                </view>
                <view class="dimensions-row">
                  <text class="dimensions-label">维度：</text>
                  <view class="dimensions-tags">
                    <view v-for="dimension in type.dimensions" :key="dimension" class="dimension-tag">
                      <text class="dimension-tag-text">{{ dimension }}</text>
                    </view>
                  </view>
                </view>
                <view class="tendency-row">
                  <text class="tendency-label">倾向：</text>
                  <view class="tendency-tags">
                    <view v-for="tendency in type.tendency" :key="tendency" class="tendency-tag">
                      <text class="tendency-tag-text">{{ tendency }}</text>
                    </view>
                  </view>
                </view>
              </view>
              
              <view class="type-description">
                <view class="type-description-label">性格描述：</view>
                <view class="type-description-text">{{ type.description }}</view>
              </view>
            </view>
          </view>
        </view>
      </view>
    </view>
  </view>
</template>

<style scoped lang="scss">
.page-wrapper {
  width: 100%;
  min-height: 100vh;
  background: rgba(26, 26, 46, 0.9);
  position: relative;
  overflow-x: hidden;
}

.personality-container {
  width: 100%;
  color: white;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
  padding-bottom: 30rpx;
}

.header-nav {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 40rpx 32rpx 20rpx;
  position: relative;
  z-index: 10;
  
  .back-btn {
    position: absolute;
    left: 32rpx;
    
    .back-icon {
      font-size: 48rpx;
      color: white;
    }
  }
  
  .nav-tabs {
    display: flex;
    background: rgba(255, 255, 255, 0.2);
    border-radius: 24rpx;
  }
  
  .nav-tab {
    padding: 16rpx 32rpx;
    border-radius: 24rpx;
    transition: all 0.3s ease;
    
    &.active {
      background: rgba(255, 255, 255, 0.9);
      
      .nav-text {
        color: #2c3e50;
      }
    }
  }
  
  .nav-text {
    font-size: 32rpx;
    font-weight: 500;
    color: white;
  }
}

.main-content {
  padding: 0 50rpx;
}

/* 动画效果 */
.rabbit-container {
  animation: gentleFloat 4s ease-in-out infinite;
}

.animated-rabbit {
  animation: gentleBreathe 3s ease-in-out infinite;
  transform-origin: center center;
}

@keyframes gentleFloat {
  0%, 100% {
    transform: translateY(0px);
  }
  50% {
    transform: translateY(-15rpx);
  }
}

@keyframes gentleBreathe {
  0%, 100% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.05);
  }
}

@keyframes floatingDots {
  0% {
    transform: translateY(0px) translateX(0px);
  }
  25% {
    transform: translateY(-10px) translateX(5px);
  }
  50% {
    transform: translateY(-5px) translateX(-3px);
  }
  75% {
    transform: translateY(-15px) translateX(8px);
  }
  100% {
    transform: translateY(0px) translateX(0px);
  }
}

@keyframes randomFloat1 {
  0% {
    transform: translateY(0px) translateX(0px);
  }
  20% {
    transform: translateY(-8px) translateX(12px);
  }
  40% {
    transform: translateY(15px) translateX(-5px);
  }
  60% {
    transform: translateY(-12px) translateX(-8px);
  }
  80% {
    transform: translateY(6px) translateX(10px);
  }
  100% {
    transform: translateY(0px) translateX(0px);
  }
}

@keyframes randomFloat2 {
  0% {
    transform: translateY(0px) translateX(0px);
  }
  15% {
    transform: translateY(10px) translateX(-7px);
  }
  35% {
    transform: translateY(-6px) translateX(14px);
  }
  55% {
    transform: translateY(18px) translateX(3px);
  }
  75% {
    transform: translateY(-14px) translateX(-11px);
  }
  100% {
    transform: translateY(0px) translateX(0px);
  }
}

@keyframes randomFloat3 {
  0% {
    transform: translateY(0px) translateX(0px);
  }
  25% {
    transform: translateY(-16px) translateX(-4px);
  }
  45% {
    transform: translateY(8px) translateX(16px);
  }
  65% {
    transform: translateY(-3px) translateX(-12px);
  }
  85% {
    transform: translateY(12px) translateX(7px);
  }
  100% {
    transform: translateY(0px) translateX(0px);
  }
}

.character-card {
  position: relative;
  height: 600rpx;
  border-radius: 32rpx;
  overflow: visible;
  margin: 40rpx 0 100rpx;
  z-index: 1;
  
  .bg-image {
    width: 100%;
    height: 100%;
    position: absolute;
    top: 0;
    left: 0;
    border-radius: 32rpx;
  }
  
  .character-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(transparent 30%, rgba(0, 0, 0, 0.3) 70%);
    display: flex;
    flex-direction: column;
    justify-content: space-between;
    padding: 60rpx 40rpx;
    border-radius: 32rpx;
    
    .character-name {
      font-size: 72rpx;
      font-weight: bold;
      text-align: center;
      margin-bottom: 40rpx;
      text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.5);
    }
    
    .traits-container {
      display: flex;
      justify-content: center;
      gap: 30rpx;
      margin-bottom: auto;
      
      .trait-tag {
        background: rgba(0, 0, 0, 0.6);
        padding: 10rpx 50rpx;
        border-radius: 40rpx;
        backdrop-filter: blur(10px);
        
        .trait-text {
          font-size: 28rpx;
          color: white;
        }
      }
    }
    
    .crystal-title {
      font-size: 50rpx;
      font-weight: bold;
      text-align: center;
      text-shadow: 2px 2px 6px rgba(0, 0, 0, 0.7);
    }
    
    .arrow-down {
      position: absolute;
      bottom: -40rpx;
      left: 50%;
      transform: translateX(-50%);
      width: 80rpx;
      height: 80rpx;
      border-radius: 50%;
      display: flex;
      align-items: center;
      justify-content: center;
      z-index: 10;
      background: #fff;
      backdrop-filter: blur(10px);
      transition: all 0.3s ease;
      cursor: pointer;
      
      &:hover {
        transform: translateX(-50%) scale(1.1);
      }
      
      &.expanded {
        background: white;
        
        image {
          transform: rotate(180deg);
        }
      }
      
      image {
        transition: transform 0.3s ease;
      }
      
      .arrow-icon {
        font-size: 40rpx;
        color: #2c3e50;
      }
    }
  }
  
  .crystal-desc {
    position: absolute;
    top: 100%;
    left: 0;
    right: 0;
    background: white;
    color: #2c3e50;
    padding: 40rpx;
    border-radius: 0 0 32rpx 32rpx;
    font-size: 32rpx;
    line-height: 1.6;
    text-align: center;
    box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.1);
    max-height: 0;
    overflow: hidden;
    opacity: 0;
    transition: all 0.3s ease;
    z-index: 5;
    
    &.expanded {
      max-height: 500rpx;
      opacity: 1;
    }
  }
}

.dimensions-section {
  padding: 0 10rpx;
  margin-bottom: 80rpx;
  
  .dimensions-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 40rpx;
    
    .dimensions-title {
      font-size: 32rpx;
      font-weight: bold;
      color: white;
    }
    
    .progress-container {
      display: flex;
      align-items: center;
      gap: 20rpx;
      
      .progress-bar {
        width: 200rpx;
        height: 8rpx;
        background: rgba(255, 255, 255, 0.3);
        border-radius: 4rpx;
        overflow: hidden;
        
        .progress-fill {
          height: 100%;
          background: linear-gradient(90deg, #ff6b35, #f7931e);
          border-radius: 4rpx;
          transition: width 0.3s ease;
        }
      }
      
      .progress-text {
        font-size: 28rpx;
        color: white;
        font-weight: 500;
      }
    }
  }
  
  .dimensions-desc {
    font-size: 32rpx;
    line-height: 1.6;
    color: rgba(255, 255, 255, 0.8);
    margin-bottom: 60rpx;
    text-align: left;
  }
  
  .start-btn {
    background: linear-gradient(90deg, #00bcd4, #2196f3);
    padding: 20rpx 32rpx;
    border-radius: 60rpx;
    text-align: center;
    margin-top: 40rpx;
    
    .start-text {
      font-size: 36rpx;
      font-weight: 600;
      color: white;
    }
  }
}

.dimensions-grid {
  margin-bottom: 60rpx;
  
  .dimension-row {
    display: flex;
    justify-content: space-between;
    margin-bottom: 20rpx;
  }
  
  .dimension-item {
    display: flex;
    flex-direction: column;
    align-items: center;
    flex: 1;
    background: rgba(0, 0, 0, 0.3);
    border-radius: 20rpx;
    padding: 20rpx;
    margin: 0 10rpx;
    
    .dimension-icon {
      border-radius: 50%;
      display: flex;
      align-items: center;
      justify-content: center;
      margin-bottom: 20rpx;
      backdrop-filter: blur(10px);
      transition: all 0.3s ease;
      
      &:hover {
        background: rgba(255, 255, 255, 0.2);
        transform: scale(1.05);
      }
      
      .icon-emoji {
        font-size: 48rpx;
      }
    }
    
    .dimension-name {
      font-size: 28rpx;
      color: rgba(255, 255, 255, 0.9);
      text-align: center;
      font-weight: 500;
    }
  }
}

.radar-section {
    padding: 0 10rpx;
  
  .radar-title {
    font-size: 32rpx;
    font-weight: 600;
    color: rgba(255, 255, 255, 0.7);
    text-align: center;
    margin-bottom: 60rpx;
    letter-spacing: 2rpx;
  }
  
  .radar-container {
    position: relative;
    width: 600rpx;
    height: 600rpx;
    margin: 0 auto;
    
    .radar-chart {
      width: 100%;
      height: 100%;
    }
    
    .radar-labels {
      position: absolute;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      
      .radar-label {
        position: absolute;
        transform: translate(-50%, -50%);
        
        .label-text {
          font-size: 28rpx;
          color: rgba(255, 255, 255, 0.8);
          font-weight: 500;
          white-space: nowrap;
        }
      }
    }
  }
}

.companion-content {

  .companion-main {
    width: 90%;
    margin: auto;
    border-radius: 30rpx;
    text-align: center;
    background: linear-gradient(135deg, #0f0f23 0%, #1a1a2e 25%, #16213e 50%, #0f3460 75%, #0f0f23 100%);
    position: relative;
    overflow: hidden;
    padding: 50rpx 0;
  }

  .companion-main::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-image: 
      radial-gradient(8px 8px at 20px 30px, rgba(255,255,255,0.8), transparent),
      radial-gradient(6px 6px at 40px 70px, rgba(255,255,255,0.6), transparent),
      radial-gradient(4px 4px at 90px 40px, rgba(255,255,255,0.9), transparent),
      radial-gradient(6px 6px at 130px 80px, rgba(255,255,255,0.7), transparent),
      radial-gradient(8px 8px at 160px 30px, rgba(255,255,255,0.5), transparent),
      radial-gradient(4px 4px at 200px 60px, rgba(255,255,255,0.8), transparent),
      radial-gradient(6px 6px at 240px 90px, rgba(255,255,255,0.6), transparent),
      radial-gradient(4px 4px at 280px 20px, rgba(255,255,255,0.9), transparent),
      radial-gradient(6px 6px at 320px 70px, rgba(255,255,255,0.7), transparent),
      radial-gradient(8px 8px at 360px 40px, rgba(255,255,255,0.5), transparent),
      radial-gradient(4px 4px at 30px 120px, rgba(255,255,255,0.8), transparent),
      radial-gradient(6px 6px at 80px 150px, rgba(255,255,255,0.6), transparent),
      radial-gradient(4px 4px at 120px 180px, rgba(255,255,255,0.9), transparent),
      radial-gradient(8px 8px at 180px 160px, rgba(255,255,255,0.7), transparent),
      radial-gradient(4px 4px at 220px 200px, rgba(255,255,255,0.5), transparent),
      radial-gradient(6px 6px at 270px 140px, rgba(255,255,255,0.8), transparent),
      radial-gradient(4px 4px at 310px 190px, rgba(255,255,255,0.6), transparent),
      radial-gradient(6px 6px at 350px 170px, rgba(255,255,255,0.9), transparent);
    background-repeat: repeat;
    background-size: 400px 250px;
    animation: randomFloat1 25s ease-in-out infinite, randomFloat2 18s ease-in-out infinite 3s, randomFloat3 22s ease-in-out infinite 7s;
    pointer-events: none;
  }

  .companion-main {
    .page-title {
      font-size: 60rpx;
      font-weight: bold;
      color: white;
      text-align: center;
      letter-spacing: 2rpx;
    }

    .companion-traits {
      display: flex;
      justify-content: center;
      gap: 30rpx;
      margin: 60rpx 0;

      .companion-trait-tag {
        background: rgba(0, 0, 0, 0.6);
        padding: 15rpx 40rpx;
        border-radius: 40rpx;
        backdrop-filter: blur(10px);

        .companion-trait-text {
          font-size: 28rpx;
          color: white;
        }
      }
    }

    .companion-character {
      position: relative;
      height: 600rpx;
      border-radius: 32rpx;
      overflow: hidden;
      margin-bottom: 60rpx;
      background: linear-gradient(135deg, #1a1a2e, #16213e);
      display: flex;
      align-items: center;
      justify-content: center;

      .character-container {
        position: relative;
        width: 100%;
        height: 100%;
        display: flex;
        align-items: center;
        justify-content: center;
      }

      .star-bg {
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        overflow: hidden;
        z-index: -1;

        .star {
          position: absolute;
          width: 2px;
          height: 2px;
          background: #fff;
          border-radius: 50%;
          opacity: 0.8;
          animation: twinkle 2s infinite;
        }
      }

      .character-avatar {
        position: relative;
        width: 300rpx;
        height: 300rpx;
        transform: scale(0.8);
        animation: float 3s ease-in-out infinite;
      }

      .character-body {
        position: relative;
        width: 100%;
        height: 100%;
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
      }

      .character-head {
        position: absolute;
        top: 0;
        left: 50%;
        transform: translateX(-50%);
        width: 100rpx;
        height: 100rpx;
        background: #4CAF50;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        z-index: 1;
      }

      .ear {
        position: absolute;
        width: 20rpx;
        height: 20rpx;
        background: #333;
        border-radius: 50%;
      }

      .ear-left {
        left: -10rpx;
      }

      .ear-right {
        right: -10rpx;
      }

      .face {
        position: absolute;
        top: 20rpx;
        left: 50%;
        transform: translateX(-50%);
        width: 80rpx;
        height: 80rpx;
        background: #fff;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
      }

      .eye {
        position: absolute;
        width: 15rpx;
        height: 15rpx;
        background: #333;
        border-radius: 50%;
      }

      .eye-left {
        left: -20rpx;
      }

      .eye-right {
        right: -20rpx;
      }

      .cheek {
        position: absolute;
        width: 10rpx;
        height: 10rpx;
        background: #333;
        border-radius: 50%;
      }

      .cheek-left {
        left: -15rpx;
      }

      .cheek-right {
        right: -15rpx;
      }

      .mouth {
        position: absolute;
        bottom: 10rpx;
        left: 50%;
        transform: translateX(-50%);
        width: 30rpx;
        height: 10rpx;
        background: #333;
        border-radius: 5rpx;
      }

      .character-torso {
        position: absolute;
        top: 100rpx;
        left: 50%;
        transform: translateX(-50%);
        width: 150rpx;
        height: 150rpx;
        background: #fff;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        z-index: 1;
      }

      .arm {
        position: absolute;
        width: 50rpx;
        height: 50rpx;
        background: #4CAF50;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
      }

      .arm-left {
        left: -20rpx;
        top: 20rpx;
      }

      .arm-right {
        right: -20rpx;
        top: 20rpx;
      }

      .hand {
        position: absolute;
        width: 30rpx;
        height: 30rpx;
        background: #4CAF50;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
      }

      .phone {
        width: 15rpx;
        height: 15rpx;
        background: #333;
        border-radius: 50%;
      }

      .shirt {
        position: absolute;
        top: 20rpx;
        left: 50%;
        transform: translateX(-50%);
        width: 100rpx;
        height: 50rpx;
        background: #4CAF50;
        border-radius: 20rpx;
        z-index: 1;
      }

      .pocket {
        position: absolute;
        bottom: 20rpx;
        left: 50%;
        transform: translateX(-50%);
        width: 80rpx;
        height: 30rpx;
        background: #4CAF50;
        border-radius: 10rpx;
        z-index: 1;
      }

      .character-legs {
        position: absolute;
        bottom: 0;
        left: 50%;
        transform: translateX(-50%);
        width: 100rpx;
        height: 100rpx;
        background: #4CAF50;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        z-index: 1;
      }

      .leg {
        position: absolute;
        width: 30rpx;
        height: 30rpx;
        background: #333;
        border-radius: 50%;
      }

      .leg-left {
        left: -10rpx;
      }

      .leg-right {
        right: -10rpx;
      }

      .shoe {
        position: absolute;
        width: 15rpx;
        height: 15rpx;
        background: #333;
        border-radius: 50%;
      }
    }

         .companion-description {
       font-size: 32rpx;
       line-height: 1.6;
       color: rgba(255, 255, 255, 0.8);
       text-align: center;
       display: flex;
       flex-direction: column;
       
       text {
         font-size: 32rpx;
         color: rgba(255, 255, 255, 0.8);
         line-height: 1.6;
       }
     }
  }
}

 .oracle-matched-section {
   padding: 50rpx 64rpx 50rpx 64rpx;

   .companion-footer {
      display: flex;
      justify-content: space-between;
      align-items: flex-start;
      margin-bottom: 50rpx;

      .compatibility-section {
        text-align: center;

        .compatibility-label {
          display: inline-flex;
          align-items: center;
          font-size: 32rpx;
          color: rgba(255, 255, 255, 0.7);
          margin-bottom: 10rpx;
        }

        .compatibility-value {
          font-size: 48rpx;
          font-weight: bold;
          color: #00bcd4;
          margin-bottom: 10rpx;
        }

        .compatibility-bar {
          width: 200rpx;
          height: 10rpx;
          background: rgba(255, 255, 255, 0.3);
          border-radius: 5rpx;
          overflow: hidden;

          .compatibility-fill {
            height: 100%;
            background: linear-gradient(90deg, #00bcd4, #2196f3);
            border-radius: 5rpx;
          }
        }
      }

      .core-dimension {
        text-align: center;

        .core-label {
          display: inline-flex;
          align-items: center;
          font-size: 32rpx;
          color: rgba(255, 255, 255, 0.7);
          margin-bottom: 10rpx;
        }

        .core-value {
          font-size: 48rpx;
          font-weight: bold;
          color: #00bcd4;
        }
      }
   }

  .oracle-matched-title {
    font-size: 32rpx;
    font-weight: 600;
    color: rgba(255, 255, 255, 0.7);
    text-align: center;
    margin-bottom: 60rpx;
    letter-spacing: 2rpx;
  }

  .radar-comparison-container {
    position: relative;
    width: 600rpx;
    height: 600rpx;
    margin: 0 auto;
    margin-bottom: 100rpx; // 为底部图例留出空间

    .radar-comparison-chart {
      width: 100%;
      height: 100%;
    }

    .radar-comparison-labels {
      position: absolute;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;

      .radar-comparison-label {
        position: absolute;
        transform: translate(-50%, -50%);

        .label-text {
          font-size: 28rpx;
          color: rgba(255, 255, 255, 0.8);
          font-weight: 500;
          white-space: nowrap;
        }
      }
    }

    .radar-comparison-legend {
      position: absolute;
      bottom: -80rpx; // 定位到雷达图下方
      left: 50%;
      transform: translateX(-50%); // 水平居中
      display: flex;
      gap: 60rpx;
      background: rgba(26, 26, 46, 0.9);
      padding: 20rpx 40rpx;
      border-radius: 40rpx;
      backdrop-filter: blur(10px);
      border: 1px solid rgba(255, 255, 255, 0.3);
      box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.3);
      width: 300rpx;

      .legend-item {
        display: flex;
        align-items: center;
        gap: 15rpx;

        .legend-color {
          width: 20rpx;
          height: 20rpx;
          border-radius: 50%;
          box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.3);
        }

        .legend-label {
          font-size: 28rpx;
          color: white;
          font-weight: 500;
          text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.5);
          margin-right: 20rpx;
        }
      }
    }
  }
}

.city-content {
  padding: 0 32rpx;
  margin-bottom: 0;
  min-height: 100vh;

  .city-main {
    text-align: center;
    padding: 20rpx 0;
    position: relative;

    .city-header {
      margin-bottom: 40rpx;
      
      .city-subtitle {
        display: block;
        font-size: 28rpx;
        color: rgba(255, 255, 255, 0.6);
        letter-spacing: 4rpx;
        font-weight: 300;
      }
      
      .city-title {
        display: block;
        font-size: 64rpx;
        font-weight: bold;
        color: white;
        margin-bottom: 10rpx;
        text-shadow: 2px 2px 8px rgba(0, 0, 0, 0.5);
        letter-spacing: 2rpx;
      }
      
      .city-type {
        display: block;
        font-size: 32rpx;
        color: rgba(255, 255, 255, 0.7);
        font-weight: 300;
      }
    }

    .city-image-container {
      position: relative;
      width: 100%;
      height: 500rpx;
      margin: 0 auto 80rpx;
      border-radius: 32rpx;
      overflow: hidden;

      .sky-background {
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background: linear-gradient(180deg, rgba(135, 206, 250, 0.8) 0%, rgba(70, 130, 180, 0.9) 100%);

        .cloud {
          position: absolute;
          width: 60rpx;
          height: 20rpx;
          background: rgba(255, 255, 255, 0.7);
          border-radius: 20rpx;
          animation: cloud-drift 15s infinite linear;
          box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
        }

        .flying-vehicle {
          position: absolute;
          top: 25%;
          left: -5%;
          width: 40rpx;
          height: 8rpx;
          background: linear-gradient(90deg, rgba(255, 255, 255, 0.9), rgba(200, 200, 200, 0.8));
          border-radius: 4rpx;
          animation: vehicle-fly 12s infinite linear;
          box-shadow: 0 2rpx 6rpx rgba(0, 0, 0, 0.2);
        }
      }

      .city-skyline {
        position: absolute;
        bottom: 0;
        left: 0;
        width: 100%;
        height: 70%;

        .city-buildings {
          position: relative;
          width: 100%;
          height: 100%;
          display: flex;
          align-items: flex-end;
          justify-content: space-around;

          .building {
            position: relative;
            background: linear-gradient(180deg, rgba(100, 149, 237, 0.9), rgba(70, 130, 180, 1));
            border-radius: 8rpx 8rpx 0 0;
            box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.3);
          }

          .building-1 {
            width: 60rpx;
            height: 200rpx;
            background: linear-gradient(180deg, rgba(220, 220, 220, 0.95), rgba(180, 180, 180, 1));
            position: relative;
            
            &::before {
              content: '';
              position: absolute;
              top: 20rpx;
              left: 50%;
              transform: translateX(-50%);
              width: 40rpx;
              height: 15rpx;
              background: rgba(139, 69, 19, 0.8);
              border-radius: 8rpx 8rpx 0 0;
            }
          }

          .building-2 {
            width: 80rpx;
            height: 280rpx;
            background: linear-gradient(180deg, rgba(245, 245, 220, 0.95), rgba(210, 180, 140, 1));
            position: relative;
            
            &::after {
              content: '';
              position: absolute;
              top: 30rpx;
              left: 10rpx;
              right: 10rpx;
              height: 40rpx;
              background: rgba(70, 130, 180, 0.6);
              border-radius: 4rpx;
            }
          }

          .building-3 {
            width: 70rpx;
            height: 320rpx;
            background: linear-gradient(180deg, rgba(255, 248, 220, 0.95), rgba(222, 184, 135, 1));
            position: relative;
            
            &::before {
              content: '';
              position: absolute;
              top: 15rpx;
              left: 50%;
              transform: translateX(-50%);
              width: 50rpx;
              height: 20rpx;
              background: rgba(178, 34, 34, 0.8);
              border-radius: 10rpx 10rpx 0 0;
            }
          }

          .building-4 {
            width: 90rpx;
            height: 240rpx;
            background: linear-gradient(180deg, rgba(240, 248, 255, 0.95), rgba(176, 196, 222, 1));
            position: relative;
            
            &::after {
              content: '';
              position: absolute;
              top: 25rpx;
              left: 15rpx;
              right: 15rpx;
              height: 30rpx;
              background: rgba(25, 25, 112, 0.7);
              border-radius: 6rpx;
            }
          }

          .building-5 {
            width: 65rpx;
            height: 180rpx;
            background: linear-gradient(180deg, rgba(250, 240, 230, 0.95), rgba(205, 133, 63, 1));
            position: relative;
            
            &::before {
              content: '';
              position: absolute;
              top: 20rpx;
              left: 50%;
              transform: translateX(-50%);
              width: 35rpx;
              height: 12rpx;
              background: rgba(160, 82, 45, 0.8);
              border-radius: 6rpx 6rpx 0 0;
            }
          }

          .city-glow {
            position: absolute;
            bottom: -20rpx;
            left: -20rpx;
            right: -20rpx;
            height: 40rpx;
            background: radial-gradient(ellipse, rgba(255, 255, 255, 0.3) 0%, transparent 70%);
            animation: city-pulse 4s ease-in-out infinite;
          }
        }
      }
    }

    .city-description {
      margin: 0 20rpx 60rpx;

      .description-text {
        font-size: 32rpx;
        line-height: 1.6;
        color: rgba(255, 255, 255, 0.8);
        text-align: center;
        letter-spacing: 1rpx;
      }
    }

    .campus-core-section {
      display: flex;
      gap: 40rpx;
      margin: 60rpx 0;
      align-items: flex-start;

      .campus-landmark-card {
        flex: 1;
        height: 300rpx;
        background: linear-gradient(135deg, #f8f9fa, #e9ecef);
        border-radius: 24rpx;
        overflow: hidden;
        position: relative;
        box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.1);

        .library-building {
          position: relative;
          width: 100%;
          height: 100%;

          .library-facade {
            position: relative;
            width: 100%;
            height: 100%;
            background: linear-gradient(180deg, #87ceeb 0%, #98d8e8 40%, #90ee90 100%);

            .main-structure {
              position: absolute;
              bottom: 25%;
              left: 50%;
              transform: translateX(-50%);
              width: 120rpx;
              height: 80rpx;
              background: linear-gradient(180deg, #f5f5dc, #deb887);
              border-radius: 8rpx 8rpx 0 0;
              box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.2);
            }

            .columns {
              position: absolute;
              bottom: 25%;
              left: 50%;
              transform: translateX(-50%);
              display: flex;
              justify-content: space-between;
              width: 100rpx;
              height: 60rpx;

              .column {
                width: 8rpx;
                height: 60rpx;
                background: rgba(255, 255, 255, 0.9);
                border-radius: 4rpx 4rpx 0 0;
                box-shadow: 0 2rpx 6rpx rgba(0, 0, 0, 0.1);
              }
            }

            .steps {
              position: absolute;
              bottom: 20%;
              left: 50%;
              transform: translateX(-50%);
              width: 140rpx;
              height: 15rpx;
              background: linear-gradient(180deg, #d3d3d3, #a9a9a9);
              border-radius: 0 0 8rpx 8rpx;
            }

            .windows {
              position: absolute;
              bottom: 40%;
              left: 50%;
              transform: translateX(-50%);
              display: flex;
              flex-wrap: wrap;
              gap: 8rpx;
              width: 80rpx;

              .window {
                width: 12rpx;
                height: 12rpx;
                background: rgba(70, 130, 180, 0.8);
                border-radius: 2rpx;
                animation: window-glow 3s ease-in-out infinite;
              }
            }

            .campus-trees {
              position: absolute;
              bottom: 15%;
              left: 0;
              width: 100%;
              height: 20%;

              .tree {
                position: absolute;
                width: 16rpx;
                height: 20rpx;
                background: radial-gradient(circle, #228b22, #006400);
                border-radius: 50% 50% 50% 50% / 60% 60% 40% 40%;
                animation: tree-sway 4s ease-in-out infinite;
              }
            }

            .student-activities {
              position: absolute;
              bottom: 10%;
              left: 0;
              width: 100%;
              height: 15%;

              .student {
                position: absolute;
                width: 6rpx;
                height: 8rpx;
                background: rgba(255, 182, 193, 0.9);
                border-radius: 50% 50% 0 0;
                animation: student-walk 5s ease-in-out infinite;
              }
            }
          }
        }
      }

      .academic-stats-section {
        flex-shrink: 0;
        display: flex;
        flex-direction: column;
        align-items: center;

        .stats-display {
          display: flex;
          flex-direction: column;
          align-items: center;
          gap: 20rpx;
          margin-bottom: 30rpx;

          .stat-item {
            text-align: center;

            .stat-value {
              font-size: 72rpx;
              font-weight: bold;
              color: white;
              text-shadow: 2px 2px 8px rgba(0, 0, 0, 0.5);
              line-height: 1;
              display: block;
            }

            .stat-label {
              font-size: 24rpx;
              color: rgba(255, 255, 255, 0.8);
              margin-top: 8rpx;
              display: block;
            }
          }

          .stats-divider {
            width: 60rpx;
            height: 2rpx;
            background: rgba(255, 255, 255, 0.5);
          }

          .academic-icon {
            position: relative;
            display: flex;
            justify-content: center;
            align-items: center;

            .book-stack {
              position: relative;
              width: 40rpx;
              height: 30rpx;

              .book {
                position: absolute;
                width: 40rpx;
                height: 8rpx;
                border-radius: 2rpx;
                box-shadow: 0 2rpx 4rpx rgba(0, 0, 0, 0.2);
                animation: book-float 3s ease-in-out infinite;
              }
            }

            .knowledge-particles {
              position: absolute;
              top: -20rpx;
              left: 50%;
              transform: translateX(-50%);

              .particle {
                position: absolute;
                width: 4rpx;
                height: 4rpx;
                background: rgba(255, 215, 0, 0.8);
                border-radius: 50%;
                animation: particle-rise 2s ease-in-out infinite;
              }
            }
          }
        }

        .activity-indicator {
          text-align: center;

          .activity-ring {
            position: relative;
            width: 80rpx;
            height: 80rpx;
            margin: 0 auto 15rpx;

            &::before {
              content: '';
              position: absolute;
              top: 0;
              left: 0;
              width: 100%;
              height: 100%;
              border: 6rpx solid rgba(255, 255, 255, 0.2);
              border-radius: 50%;
            }

            .ring-progress {
              position: absolute;
              top: 0;
              left: 0;
              width: 100%;
              height: 100%;
              border: 6rpx solid transparent;
              border-top-color: #4ecdc4;
              border-right-color: #4ecdc4;
              border-bottom-color: #4ecdc4;
              border-radius: 50%;
              transform-origin: center;
              animation: ring-rotate 3s ease-in-out infinite;
            }
          }

          .activity-text {
            font-size: 22rpx;
            color: rgba(255, 255, 255, 0.9);
            font-weight: 500;
          }
        }
      }
    }

    .city-info-with-thumbnails {
      display: flex;
      gap: 40rpx;
      margin: 40rpx 0;
      align-items: flex-start;
    }

    .city-info-section {
      flex: 1;

      .city-info-text {
        font-size: 24rpx;
        line-height: 1.6;
        color: rgba(255, 255, 255, 0.8);
        text-align: left;
        letter-spacing: 1rpx;
      }
    }

    .thumbnail-grid {
      display: flex;
      flex-direction: column;
      gap: 15rpx;
      flex-shrink: 0;
      width: 240rpx;

      .thumbnail-row {
        display: flex;
        gap: 15rpx;
      }

      .thumbnail-item {
        flex: 1;

        .thumbnail-image {
          width: 100%;
          height: 80rpx;
          border-radius: 12rpx;
          overflow: hidden;
          position: relative;

          .thumb-landscape {
            position: relative;
            width: 100%;
            height: 100%;
          }
        }

        .thumbnail-1 {
          background: linear-gradient(135deg, #f5f5dc, #deb887);

          .thumb-library-sky {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 35%;
            background: linear-gradient(180deg, #87ceeb, #b0e0e6);
          }

          .thumb-library-building {
            position: absolute;
            bottom: 25%;
            left: 50%;
            transform: translateX(-50%);
            width: 60%;
            height: 40%;
            background: linear-gradient(180deg, #f5f5dc, #deb887);
            border-radius: 4rpx 4rpx 0 0;
            
            &::before {
              content: '';
              position: absolute;
              top: 10%;
              left: 10%;
              right: 10%;
              height: 20%;
              background: rgba(70, 130, 180, 0.6);
              border-radius: 2rpx;
            }
          }

          .thumb-library-steps {
            position: absolute;
            bottom: 20%;
            left: 50%;
            transform: translateX(-50%);
            width: 70%;
            height: 8%;
            background: #d3d3d3;
          }

          .thumb-library-trees {
            position: absolute;
            bottom: 15%;
            left: 15%;
            width: 15%;
            height: 15%;
            background: radial-gradient(circle, #228b22, #006400);
            border-radius: 50%;
          }
        }

        .thumbnail-2 {
          background: linear-gradient(135deg, #90ee90, #32cd32);

          .thumb-sports-sky {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 40%;
            background: linear-gradient(180deg, #87ceeb, #98d8e8);
          }

          .thumb-sports-field {
            position: absolute;
            bottom: 20%;
            left: 50%;
            transform: translateX(-50%);
            width: 70%;
            height: 35%;
            background: linear-gradient(180deg, #32cd32, #228b22);
            border-radius: 50%;
          }

          .thumb-sports-track {
            position: absolute;
            bottom: 15%;
            left: 50%;
            transform: translateX(-50%);
            width: 80%;
            height: 45%;
            border: 3rpx solid #8b4513;
            border-radius: 50%;
            background: transparent;
          }

          .thumb-sports-stands {
            position: absolute;
            bottom: 35%;
            left: 10%;
            width: 20%;
            height: 15%;
            background: linear-gradient(to top, #696969, #a9a9a9);
            clip-path: polygon(0% 100%, 100% 100%, 80% 0%, 20% 0%);
          }
        }

        .thumbnail-3 {
          background: linear-gradient(135deg, #e6f3ff, #cce7ff);

          .thumb-lab-sky {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 40%;
            background: linear-gradient(180deg, #4a90e2, #74b9ff);
          }

          .thumb-lab-building {
            position: absolute;
            bottom: 20%;
            left: 50%;
            transform: translateX(-50%);
            width: 65%;
            height: 45%;
            background: linear-gradient(180deg, #f0f8ff, #b0c4de);
            border-radius: 2rpx;
          }

          .thumb-lab-windows {
            position: absolute;
            bottom: 35%;
            left: 50%;
            transform: translateX(-50%);
            width: 50%;
            height: 20%;
            display: flex;
            flex-wrap: wrap;
            gap: 2rpx;
            
            &::before, &::after {
              content: '';
              width: 8rpx;
              height: 8rpx;
              background: rgba(70, 130, 180, 0.8);
              border-radius: 1rpx;
            }
          }

          .thumb-lab-ground {
            position: absolute;
            bottom: 0;
            left: 0;
            width: 100%;
            height: 20%;
            background: #708090;
          }
        }

        .thumbnail-4 {
          background: linear-gradient(135deg, #ffe4e1, #ffc0cb);

          .thumb-dorm-sky {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 45%;
            background: linear-gradient(180deg, #87ceeb, #b0e0e6);
          }

          .thumb-dorm-buildings {
            position: absolute;
            bottom: 25%;
            left: 0;
            width: 100%;
            height: 30%;
            background: linear-gradient(to top, #ff7043, #ffab40);
            clip-path: polygon(0% 100%, 25% 60%, 50% 80%, 75% 50%, 100% 100%);
          }

          .thumb-dorm-courtyard {
            position: absolute;
            bottom: 15%;
            left: 50%;
            transform: translateX(-50%);
            width: 60%;
            height: 15%;
            background: #90ee90;
            border-radius: 4rpx;
          }

          .thumb-dorm-path {
            position: absolute;
            bottom: 0;
            left: 0;
            width: 100%;
            height: 15%;
            background: #d2b48c;
          }
        }
      }
    }
  }
}

@keyframes twinkle {
  0%, 100% { opacity: 0.8; }
  50% { opacity: 0.2; }
}

@keyframes float {
  0%, 100% { transform: translateY(0); }
  50% { transform: translateY(-20rpx); }
}

@keyframes space-twinkle {
  0%, 100% { 
    opacity: 0.8; 
    transform: scale(1);
  }
  50% { 
    opacity: 0.2; 
    transform: scale(1.2);
  }
}

@keyframes meteor-fly {
  0% {
    left: -10%;
    opacity: 0;
  }
  10% {
    opacity: 1;
  }
  90% {
    opacity: 1;
  }
  100% {
    left: 110%;
    opacity: 0;
  }
}

@keyframes planet-rotate {
  0% { transform: translate(-50%, -50%) rotate(0deg); }
  100% { transform: translate(-50%, -50%) rotate(360deg); }
}

@keyframes planet-pulse {
  0%, 100% { 
    opacity: 0.6;
    transform: scale(1);
  }
  50% { 
    opacity: 0.9;
    transform: scale(1.05);
  }
}

@keyframes pattern-glow {
  0%, 100% { 
    opacity: 0.6;
    transform: scale(1);
    box-shadow: 0 0 20rpx rgba(0, 255, 150, 0.3);
  }
  50% { 
    opacity: 0.9;
    transform: scale(1.1);
    box-shadow: 0 0 40rpx rgba(0, 255, 150, 0.6);
  }
}

@keyframes water-ripple {
  0% {
    width: 20rpx;
    height: 20rpx;
    opacity: 0.8;
  }
  100% {
    width: 200rpx;
    height: 200rpx;
    opacity: 0;
  }
}

@keyframes plant-glow {
  0%, 100% { 
    opacity: 0.7;
    transform: scaleY(1);
    box-shadow: 0 0 10rpx rgba(0, 255, 100, 0.5);
  }
  50% { 
    opacity: 1;
    transform: scaleY(1.2);
    box-shadow: 0 0 20rpx rgba(0, 255, 100, 0.8);
  }
}

@keyframes animal-move {
  0%, 100% { 
    transform: translateX(0);
  }
  25% { 
    transform: translateX(20rpx);
  }
  75% { 
    transform: translateX(-10rpx);
  }
}

@keyframes cloud-drift {
  0% {
    left: -10%;
    opacity: 0.7;
  }
  50% {
    opacity: 1;
  }
  100% {
    left: 110%;
    opacity: 0.7;
  }
}

@keyframes vehicle-fly {
  0% {
    left: -5%;
    opacity: 0.8;
  }
  100% {
    left: 105%;
    opacity: 0.8;
  }
}

@keyframes city-pulse {
  0%, 100% { 
    opacity: 0.3;
    transform: scaleY(1);
  }
  50% { 
    opacity: 0.6;
    transform: scaleY(1.2);
  }
}

@keyframes vehicle-move {
  0%, 100% { 
    transform: translateX(0);
  }
  25% { 
    transform: translateX(15rpx);
  }
  75% { 
    transform: translateX(-8rpx);
  }
}

@keyframes growth-pulse {
  0%, 100% { 
    opacity: 0.6;
    transform: scale(1);
  }
  50% { 
    opacity: 1;
    transform: scale(1.3);
  }
}

@keyframes window-glow {
  0%, 100% { 
    opacity: 0.8;
    box-shadow: 0 0 8rpx rgba(70, 130, 180, 0.5);
  }
  50% { 
    opacity: 1;
    box-shadow: 0 0 16rpx rgba(70, 130, 180, 0.8);
  }
}

@keyframes tree-sway {
  0%, 100% { 
    transform: rotate(0deg);
  }
  25% { 
    transform: rotate(2deg);
  }
  75% { 
    transform: rotate(-2deg);
  }
}

@keyframes student-walk {
  0%, 100% { 
    transform: translateX(0);
  }
  25% { 
    transform: translateX(10rpx);
  }
  75% { 
    transform: translateX(-5rpx);
  }
}

@keyframes book-float {
  0%, 100% { 
    transform: translateY(0);
  }
  50% { 
    transform: translateY(-5rpx);
  }
}

@keyframes particle-rise {
  0% { 
    opacity: 0;
    transform: translateY(0) scale(0.5);
  }
  50% { 
    opacity: 1;
    transform: translateY(-20rpx) scale(1);
  }
  100% { 
    opacity: 0;
    transform: translateY(-40rpx) scale(0.5);
  }
}

@keyframes ring-rotate {
  0% { 
    transform: rotate(0deg);
  }
  100% { 
    transform: rotate(360deg);
  }
}

@keyframes floating-lights {
  0%, 100% {
    background: radial-gradient(circle at 20% 30%, rgba(255, 255, 255, 0.1) 0%, transparent 50%),
                radial-gradient(circle at 80% 20%, rgba(255, 255, 255, 0.08) 0%, transparent 50%),
                radial-gradient(circle at 40% 70%, rgba(255, 255, 255, 0.06) 0%, transparent 50%),
                radial-gradient(circle at 90% 80%, rgba(255, 255, 255, 0.1) 0%, transparent 50%),
                radial-gradient(circle at 10% 90%, rgba(255, 255, 255, 0.05) 0%, transparent 50%);
  }
  25% {
    background: radial-gradient(circle at 30% 40%, rgba(255, 255, 255, 0.12) 0%, transparent 50%),
                radial-gradient(circle at 70% 30%, rgba(255, 255, 255, 0.09) 0%, transparent 50%),
                radial-gradient(circle at 50% 80%, rgba(255, 255, 255, 0.07) 0%, transparent 50%),
                radial-gradient(circle at 85% 70%, rgba(255, 255, 255, 0.11) 0%, transparent 50%),
                radial-gradient(circle at 15% 80%, rgba(255, 255, 255, 0.06) 0%, transparent 50%);
  }
  50% {
    background: radial-gradient(circle at 40% 50%, rgba(255, 255, 255, 0.11) 0%, transparent 50%),
                radial-gradient(circle at 60% 40%, rgba(255, 255, 255, 0.08) 0%, transparent 50%),
                radial-gradient(circle at 60% 90%, rgba(255, 255, 255, 0.08) 0%, transparent 50%),
                radial-gradient(circle at 80% 60%, rgba(255, 255, 255, 0.12) 0%, transparent 50%),
                radial-gradient(circle at 20% 70%, rgba(255, 255, 255, 0.07) 0%, transparent 50%);
  }
  75% {
    background: radial-gradient(circle at 50% 60%, rgba(255, 255, 255, 0.09) 0%, transparent 50%),
                radial-gradient(circle at 50% 50%, rgba(255, 255, 255, 0.10) 0%, transparent 50%),
                radial-gradient(circle at 70% 100%, rgba(255, 255, 255, 0.06) 0%, transparent 50%),
                radial-gradient(circle at 75% 50%, rgba(255, 255, 255, 0.09) 0%, transparent 50%),
                radial-gradient(circle at 25% 60%, rgba(255, 255, 255, 0.08) 0%, transparent 50%);
  }
}

@keyframes floating-lights-secondary {
  0%, 100% {
    background: radial-gradient(circle at 60% 40%, rgba(64, 224, 208, 0.15) 0%, transparent 30%),
                radial-gradient(circle at 30% 60%, rgba(64, 224, 208, 0.1) 0%, transparent 30%),
                radial-gradient(circle at 70% 80%, rgba(64, 224, 208, 0.12) 0%, transparent 30%);
  }
  33% {
    background: radial-gradient(circle at 70% 50%, rgba(64, 224, 208, 0.12) 0%, transparent 30%),
                radial-gradient(circle at 40% 70%, rgba(64, 224, 208, 0.08) 0%, transparent 30%),
                radial-gradient(circle at 80% 70%, rgba(64, 224, 208, 0.14) 0%, transparent 30%);
  }
  66% {
    background: radial-gradient(circle at 80% 60%, rgba(64, 224, 208, 0.10) 0%, transparent 30%),
                radial-gradient(circle at 50% 80%, rgba(64, 224, 208, 0.12) 0%, transparent 30%),
                radial-gradient(circle at 90% 60%, rgba(64, 224, 208, 0.11) 0%, transparent 30%);
  }
}

/* 弹窗样式 */
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.6);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 10000;
  backdrop-filter: blur(10rpx);
}

.modal-content {
  background: white;
  border-radius: 32rpx;
  width: 90%;
  max-width: 600rpx;
  max-height: 80vh;
  overflow: hidden;
  box-shadow: 0 20rpx 60rpx rgba(0, 0, 0, 0.3);
  animation: modal-slide-up 0.3s ease-out;
}

.modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 40rpx 40rpx 20rpx;
  border-bottom: 1rpx solid rgba(0, 0, 0, 0.1);
}

.modal-title {
  font-size: 48rpx;
  font-weight: bold;
  color: #2c3e50;
  letter-spacing: 1rpx;
}

.modal-close {
  width: 60rpx;
  height: 60rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  cursor: pointer;
  transition: all 0.2s ease;
  
  &:hover {
    background: rgba(0, 0, 0, 0.1);
    transform: scale(1.1);
  }
}

.close-icon {
  font-size: 32rpx;
  font-weight: bold;
}

.modal-body {
  padding: 20rpx 40rpx 40rpx;
}

.modal-text {
  font-size: 32rpx;
  line-height: 1.6;
  color: #555;
  text-align: left;
  letter-spacing: 0.5rpx;
}

@keyframes modal-slide-up {
  0% {
    opacity: 0;
    transform: translateY(100rpx) scale(0.9);
  }
  100% {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}

/* 性格类型弹窗特殊样式 */
.personality-modal {
  max-height: 85vh;
  
  .modal-body {
    max-height: calc(85vh - 120rpx);
    overflow-y: auto;
  }
}

.current-personality {
  background: linear-gradient(135deg, #667eea, #764ba2);
  border-radius: 24rpx;
  padding: 30rpx;
  margin-bottom: 30rpx;
  color: white;
}

.personality-header {
  text-align: center;
  margin-bottom: 30rpx;
  
  .personality-name {
    font-size: 36rpx;
    font-weight: bold;
    display: block;
    margin-bottom: 10rpx;
  }
  
  .personality-subtitle {
    font-size: 24rpx;
    opacity: 0.8;
  }
}

.personality-traits,
.personality-dimensions {
  margin-bottom: 25rpx;
  
  .traits-title,
  .dimensions-title {
    font-size: 28rpx;
    font-weight: 600;
    margin-bottom: 15rpx;
    display: block;
  }
  
  .traits-list,
  .dimensions-list {
    display: flex;
    flex-wrap: wrap;
    gap: 12rpx;
  }
  
  .trait-item,
  .dimension-item {
    background: rgba(255, 255, 255, 0.2);
    padding: 8rpx 16rpx;
    border-radius: 16rpx;
    border: 1rpx solid rgba(255, 255, 255, 0.3);
    
    &.current {
      background: rgba(255, 255, 255, 0.3);
    }
    
    .trait-text,
    .dimension-text {
      font-size: 24rpx;
      color: white;
      font-weight: 500;
    }
  }
}

.personality-description {
  .description-content {
    font-size: 26rpx;
    line-height: 1.6;
    color: rgba(255, 255, 255, 0.9);
    text-align: left;
    letter-spacing: 0.5rpx;
  }
}

.personality-divider {
  height: 2rpx;
  background: linear-gradient(90deg, transparent, #e0e0e0, transparent);
  margin: 30rpx 0;
}

.all-personalities {
  .all-personalities-title {
    font-size: 32rpx;
    font-weight: bold;
    color: #2c3e50;
    text-align: center;
    margin-bottom: 30rpx;
    display: block;
  }
}

.personality-type-item {
  border: 2rpx solid #f0f0f0;
  border-radius: 20rpx;
  padding: 25rpx;
  margin-bottom: 20rpx;
  transition: all 0.3s ease;
  
  &.active {
    border-color: #667eea;
    background: linear-gradient(135deg, rgba(102, 126, 234, 0.05), rgba(118, 75, 162, 0.05));
    box-shadow: 0 4rpx 20rpx rgba(102, 126, 234, 0.1);
  }
  
  &:hover {
    border-color: #667eea;
    transform: translateY(-2rpx);
    box-shadow: 0 8rpx 25rpx rgba(0, 0, 0, 0.1);
  }
}

.type-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20rpx;
  
  .type-name {
    font-size: 30rpx;
    font-weight: bold;
    color: #2c3e50;
  }
  
  .current-badge {
    background: linear-gradient(135deg, #667eea, #764ba2);
    padding: 0 12rpx;
    border-radius: 12rpx;
    
    .badge-text {
      font-size: 20rpx;
      color: white;
      font-weight: 500;
    }
  }
}

.type-traits {
  margin-bottom: 20rpx;
  
  .traits-row,
  .dimensions-row,
  .tendency-row {
    display: flex;
    align-items: flex-start;
    margin-bottom: 15rpx;
    
    .traits-label,
    .dimensions-label,
    .tendency-label {
      font-size: 26rpx;
      color: #666;
      min-width: 80rpx;
      margin-right: 15rpx;
      margin-top: 8rpx;
    }
    
    .traits-tags,
    .dimensions-tags,
    .tendency-tags {
      display: flex;
      flex-wrap: wrap;
      gap: 8rpx;
      flex: 1;
    }
    
    .trait-tag,
    .dimension-tag,
    .tendency-tag {
      background: #f8f9fa;
      border: 1rpx solid #e9ecef;
      padding: 0 12rpx;
      border-radius: 12rpx;
      
      .trait-tag-text,
      .dimension-tag-text,
      .tendency-tag-text {
        font-size: 22rpx;
        color: #495057;
      }
    }
    
    // 特质标签高亮
    .trait-tag {
      background: linear-gradient(135deg, #667eea, #764ba2);
      border: 1rpx solid #667eea;
      
      .trait-tag-text {
        color: white;
        font-weight: 500;
      }
    }
    
    // 维度标签高亮
    .dimension-tag {
      background: linear-gradient(135deg, #667eea, #764ba2);
      border: 1rpx solid #667eea;
      
      .dimension-tag-text {
        color: white;
        font-weight: 500;
      }
    }
    
    // 倾向标签高亮
    .tendency-tag {
      background: linear-gradient(135deg, #667eea, #764ba2);
      border: 1rpx solid #667eea;
      
      .tendency-tag-text {
        color: white;
        font-weight: 500;
      }
    }
  }
}

.type-description {
  .type-description-label{
    font-size: 26rpx;
    color: #666;
    margin-bottom: 10rpx;
    display: block;
  }
  .type-description-text {
    font-size: 26rpx;
    line-height: 1.6;
    color: #666;
    text-align: left;
    letter-spacing: 0.3rpx;
  }
}
</style>