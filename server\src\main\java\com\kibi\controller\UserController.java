package com.kibi.controller;

import com.kibi.entity.User;
import com.kibi.service.UserService;
import com.kibi.utils.JWTUtils;
import com.kibi.utils.R;
import me.chanjar.weixin.common.api.WxConsts;
import me.chanjar.weixin.common.bean.WxJsapiSignature;
import me.chanjar.weixin.common.bean.WxOAuth2UserInfo;
import me.chanjar.weixin.common.bean.oauth2.WxOAuth2AccessToken;
import me.chanjar.weixin.common.error.WxErrorException;
import me.chanjar.weixin.mp.api.WxMpService;
import org.springframework.web.bind.annotation.*;

import java.time.LocalDate;
import java.time.ZoneId;
import java.util.Date;
import java.util.HashMap;
import java.util.Map;

@RestController
@RequestMapping("/user")
public class UserController {

    private final WxMpService wxMpService;
    private final UserService userService;
    private final JWTUtils jwtUtils;

    public UserController(WxMpService wxMpService, UserService userService, JWTUtils jwtUtils) {
        this.wxMpService = wxMpService;
        this.userService = userService;
        this.jwtUtils = jwtUtils;
    }

    //微信js-sdk签名 - 公开接口，不需要token验证
    @GetMapping("/signature")
    public R<WxJsapiSignature> getSignature(String url) {
        try {
            System.out.println("获取微信签名，URL: " + url);
            WxJsapiSignature jsapiSignature = wxMpService.createJsapiSignature(url);
            System.out.println("签名生成成功: " + jsapiSignature);
            return R.success(jsapiSignature);
        } catch (WxErrorException e) {
            System.err.println("微信API调用失败: " + e.getMessage());
            return R.error("微信签名获取失败: " + e.getMessage());
        } catch (Exception e) {
            System.err.println("签名生成异常: " + e.getMessage());
            return R.error("签名生成失败");
        }
    }

    //微信网页授权 - 公开接口，不需要token验证
    @GetMapping("/url")
    public R<String> buildUrl(String url) {
        try {
            String buildUrl = wxMpService.getOAuth2Service().buildAuthorizationUrl(url, WxConsts.OAuth2Scope.SNSAPI_USERINFO, null);
            return R.success(buildUrl);
        } catch (Exception e) {
            System.err.println("构建授权URL失败: " + e.getMessage());
            return R.error("构建授权URL失败");
        }
    }

    // 登录接口 - 公开接口，不需要token验证
    @GetMapping("/login")
    public R<User> login(String code) {
        try {
            // 获取访问令牌
            WxOAuth2AccessToken wxOAuth2AccessToken = wxMpService.getOAuth2Service().getAccessToken(code);
            
            // 查询用户是否已存在
            User existingUser = selectUserByOpenid(wxOAuth2AccessToken.getOpenId());
            
            if (existingUser != null) {
                // 用户已存在，生成token并返回
                String token = jwtUtils.generateJwtToken(existingUser.getId(), existingUser.getNickName());
                existingUser.setToken(token);
                existingUser.setOpenid(null); // 隐藏openid
                return R.success(existingUser);
            }
            
            // 用户不存在，获取用户信息并创建新用户
            WxOAuth2UserInfo wxMpUser = wxMpService.getOAuth2Service().getUserInfo(wxOAuth2AccessToken, null);
            User user = new User();
            user.setOpenid(wxMpUser.getOpenid());
            user.setNickName(wxMpUser.getNickname());
            user.setHeadImgUrl(wxMpUser.getHeadImgUrl());
            user.setSignIn(Date.from(LocalDate.now().minusDays(1).atStartOfDay(ZoneId.systemDefault()).toInstant()));
            user.setTokens(10); //设置算力为10
            user.setStatus(1); // 设置状态为正常
            
            // 保存用户
            boolean b = userService.save(user);
            if (b) {
                // 获取新创建的用户
                User newUser = selectUserByOpenid(wxMpUser.getOpenid());
                if (newUser != null) {
                    // 生成token
                    String token = jwtUtils.generateJwtToken(newUser.getId(), newUser.getNickName());
                    newUser.setToken(token);
                    newUser.setOpenid(null); // 隐藏openid
                    return R.success(newUser);
                }
            }
            
            return R.error("用户创建失败");
            
        } catch (WxErrorException e) {
            System.err.println("微信授权失败: " + e.getMessage());
            return R.error("微信授权失败: " + e.getMessage());
        } catch (Exception e) {
            System.err.println("登录处理异常: " + e.getMessage());
            return R.error("登录失败");
        }
    }
    
    /**
     * 开发环境模拟登录接口 - 公开接口，不需要token验证
     * 用于开发调试，返回模拟用户数据
     */
    @GetMapping("/mock-login")
    public R<User> mockLogin() {
        try {
            User user = userService.getById(88888L);
            
            // 隐藏敏感信息
            user.setOpenid(null);
            String token = jwtUtils.generateJwtToken(user.getId(), user.getNickName());
            user.setToken(token);
            
            return R.success(user);
            
        } catch (Exception e) {
            System.err.println("模拟登录异常: " + e.getMessage());
            return R.error("模拟登录失败");
        }
    }

    /**
     * 签到 - 需要token验证
     */
    @PostMapping("/signIn")
    public R signIn(@RequestHeader("Authorization") String authHeader) {
        try {
            System.out.println("开始执行签到...");

            // 验证token
            String token = jwtUtils.extractTokenFromHeader(authHeader);
            if (token == null || !jwtUtils.validateToken(token)) {
                return R.error("用户未登录或token无效");
            }

            // 从token中获取用户ID
            Long userId = jwtUtils.getUserIdFromToken(token);
            System.out.println("签到用户ID: " + userId);

            if (userId == null) {
                System.out.println("用户未登录");
                return R.error("用户未登录");
            }

            User user = userService.getById(userId);
            if (user == null) {
                return R.error("用户不存在");
            }

            if (user.getStatus() != 1) {
                return R.error("账户已被禁用");
            }

            LocalDate today = LocalDate.now();
            Date lastSignInDate = user.getSignIn();
            LocalDate lastSignIn = null;

            // 将Date转换为LocalDate进行比较
            if (lastSignInDate != null) {
                lastSignIn = lastSignInDate.toInstant()
                    .atZone(ZoneId.systemDefault())
                    .toLocalDate();
            }

            // 检查是否已经签到
            if (lastSignIn != null && lastSignIn.equals(today)) {
                return R.error("今天已经签到过了");
            }

            // 更新签到时间和奖励
            user.setSignIn(Date.from(today.atStartOfDay(ZoneId.systemDefault()).toInstant()));
            user.setTokens(user.getTokens() + 5); // 签到奖励5算力

            boolean b = userService.updateById(user);

            if (b) {
                Map<String, Object> data = new HashMap<>();
                data.put("tokens", user.getTokens());
                data.put("signInDate", today.toString());
                data.put("reward", 5);
                return R.success(data);
            } else {
                System.out.println("数据库更新失败");
                return R.error("签到失败，请稍后重试");
            }
        } catch (Exception e) {
            System.err.println("签到异常: " + e.getMessage());
            e.printStackTrace();
            return R.error("签到失败，请稍后重试");
        }
    }

    /**
     * 查询签到状态 - 需要token验证
     */
    @GetMapping("/signInStatus")
    public R getSignInStatus(@RequestHeader("Authorization") String authHeader) {
        try {
            System.out.println("开始查询签到状态...");

            // 验证token
            String token = jwtUtils.extractTokenFromHeader(authHeader);
            if (token == null || !jwtUtils.validateToken(token)) {
                return R.error("用户未登录或token无效");
            }

            // 从token中获取用户ID
            Long userId = jwtUtils.getUserIdFromToken(token);
            System.out.println("解析到的用户ID: " + userId);

            if (userId == null) {
                System.out.println("用户未登录");
                return R.error("用户未登录");
            }

            User user = userService.getById(userId);
            if (user == null) {
                System.out.println("用户不存在: " + userId);
                return R.error("用户不存在");
            }

            LocalDate today = LocalDate.now();
            Date lastSignInDate = user.getSignIn();
            boolean hasSignedToday = false;

            System.out.println("今日日期: " + today);
            System.out.println("最后签到时间: " + lastSignInDate);

            // 将Date转换为LocalDate进行比较
            if (lastSignInDate != null) {
                LocalDate lastSignIn = lastSignInDate.toInstant()
                    .atZone(ZoneId.systemDefault())
                    .toLocalDate();
                hasSignedToday = lastSignIn.equals(today);
                System.out.println("最后签到日期: " + lastSignIn);
                System.out.println("今日是否已签到: " + hasSignedToday);
            } else {
                System.out.println("用户从未签到");
            }

            Map<String, Object> data = new HashMap<>();
            data.put("hasSignedToday", hasSignedToday);
            data.put("tokens", user.getTokens());
            data.put("lastSignInDate", lastSignInDate != null ? lastSignInDate.toString() : null);

            System.out.println("返回签到状态数据: " + data);
            return R.success(data);
        } catch (Exception e) {
            System.err.println("查询签到状态异常: " + e.getMessage());
            e.printStackTrace();
            return R.error("查询签到状态失败");
        }
    }

    /**
     * 更新用户昵称 - 需要token验证
     */
    @PostMapping("/updateNickname")
    public R updateNickname(String nickname, @RequestHeader("Authorization") String authHeader) {
        try {
            // 验证token
            String token = jwtUtils.extractTokenFromHeader(authHeader);
            if (token == null || !jwtUtils.validateToken(token)) {
                return R.error("用户未登录或token无效");
            }

            // 从token中获取用户ID
            Long userId = jwtUtils.getUserIdFromToken(token);
            if (userId == null) {
                return R.error("用户未登录");
            }

            if (nickname == null || nickname.trim().isEmpty()) {
                return R.error("昵称不能为空");
            }

            if (nickname.trim().length() > 20) {
                return R.error("昵称长度不能超过20个字符");
            }

            User user = userService.getById(userId);
            if (user == null) {
                return R.error("用户不存在");
            }

            user.setNickName(nickname.trim());
            boolean b = userService.updateById(user);

            if (b) {
                Map<String, Object> data = new HashMap<>();
                data.put("nickname", user.getNickName());
                return R.success(data);
            } else {
                return R.error("更新失败，请稍后重试");
            }
        } catch (Exception e) {
            System.err.println("更新昵称异常: " + e.getMessage());
            e.printStackTrace();
            return R.error("更新失败");
        }
    }

    /**
     * 更新用户联系号码 - 需要token验证
     */
    @PostMapping("/updatePhone")
    public R updatePhone(String phone, @RequestHeader("Authorization") String authHeader) {
        try {
            // 验证token
            String token = jwtUtils.extractTokenFromHeader(authHeader);
            if (token == null || !jwtUtils.validateToken(token)) {
                return R.error("用户未登录或token无效");
            }

            // 从token中获取用户ID
            Long userId = jwtUtils.getUserIdFromToken(token);
            if (userId == null) {
                return R.error("用户未登录");
            }

            if (phone == null || phone.trim().isEmpty()) {
                return R.error("联系号码不能为空");
            }

            // 简单的手机号格式验证
            String phonePattern = "^1[3-9]\\d{9}$";
            if (!phone.trim().matches(phonePattern)) {
                return R.error("请输入正确的手机号码");
            }

            User user = userService.getById(userId);
            if (user == null) {
                return R.error("用户不存在");
            }

            user.setPhone(phone.trim());
            boolean b = userService.updateById(user);

            if (b) {
                Map<String, Object> data = new HashMap<>();
                data.put("phone", user.getPhone());
                return R.success(data);
            } else {
                return R.error("更新失败，请稍后重试");
            }
        } catch (Exception e) {
            System.err.println("更新联系号码异常: " + e.getMessage());
            e.printStackTrace();
            return R.error("更新失败");
        }
    }

    /**
     * 检查token是否有效 - 需要token验证
     */
    @GetMapping("/validateToken")
    public R validateToken(@RequestHeader("Authorization") String authHeader) {
        try {
            System.out.println("开始验证token...");

            // 验证token
            String token = jwtUtils.extractTokenFromHeader(authHeader);
            if (token == null || !jwtUtils.validateToken(token)) {
                System.out.println("Token无效或已过期");
                return R.error("Token无效或已过期");
            }

            // 从token中获取用户ID
            Long userId = jwtUtils.getUserIdFromToken(token);
            System.out.println("Token验证成功，用户ID: " + userId);

            if (userId == null) {
                System.out.println("无法从token中获取用户ID");
                return R.error("Token格式错误");
            }

            // 验证用户是否存在且状态正常
            User user = userService.getById(userId);
            if (user == null) {
                System.out.println("用户不存在: " + userId);
                return R.error("用户不存在");
            }

            if (user.getStatus() != 1) {
                System.out.println("用户账户已被禁用: " + userId);
                return R.error("账户已被禁用");
            }

            System.out.println("Token验证通过");
            return R.success(true);
        } catch (Exception e) {
            System.err.println("Token验证异常: " + e.getMessage());
            e.printStackTrace();
            return R.error("Token验证失败");
        }
    }

    /**
     * 根据openid查询用户
     */
    private User selectUserByOpenid(String openid) {
        return userService.lambdaQuery()
                .eq(User::getOpenid, openid)
                .one();
    }
}