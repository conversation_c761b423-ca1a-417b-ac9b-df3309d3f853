<script setup lang="ts">
import { onLaunch, onShow, onHide } from "@dcloudio/uni-app";
import { http } from '@/utils/http'
import { useUserStore } from '@/stores'

const userStore = useUserStore()

// 检查token是否有效
const validateToken = async () => {
  try {
    // 检查是否有用户信息和token
    if (!userStore.profile?.token) {
      return
    }
    
    const isValid = await http<boolean>({
      method: 'GET',
      url: '/user/validateToken'
    })
    
    if (isValid.code != 200) {
      userStore.clearProfile()
      return
    }

  } catch (error) {
    userStore.clearProfile()
    uni.showToast({
      title: '网络异常',
      icon: 'none'
    })
  }
}

onLaunch(() => {
  // 应用启动时验证token
  validateToken()
});

onShow(() => {
});

onHide(() => {
});
</script>

<style lang="scss">
page {
  height: 100%;
}

//让uni.showToast提示信息在最上层显示
uni-toast{
  z-index: 19999 !important;
}

//让uni.showModal提示框在最上层显示
uni-modal{
  z-index:19999 !important;
}
</style>
